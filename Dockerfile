# Stage 1: Install dependencies
FROM node:lts AS deps
WORKDIR /app

# Copy ONLY package.json and lock first for better caching
COPY package.json package-lock.json ./
RUN npm install

# Stage 2: Build
FROM node:lts AS builder
WORKDIR /app

COPY --from=deps /app/node_modules ./node_modules
COPY . . 
RUN npx prisma generate
RUN npm run build

# Stage 3: Production image
FROM node:lts AS runner
WORKDIR /app

COPY --from=builder /app/node_modules ./node_modules
COPY --from=builder /app/dist ./dist
COPY --from=builder /app/prisma ./prisma
COPY package.json . 

CMD npx prisma migrate deploy && node dist/main.js