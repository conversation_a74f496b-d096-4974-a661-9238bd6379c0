import { Test, TestingModule } from '@nestjs/testing';
import { TransactionsService } from './transactions.service';
import { DatabaseService } from '../database/database.service';
import { BadRequestException, NotFoundException } from '@nestjs/common';
import { CreateTransactionDto } from '../dto/create-transaction.dto';
import { UpdateTransactionDto } from '../dto/update-transaction.dto';

describe('TransactionsService', () => {
  let service: TransactionsService;
  let databaseService: DatabaseService;

  const mockDatabaseService = {
    transaction: {
      create: jest.fn(),
      findMany: jest.fn(),
      count: jest.fn(),
      findUnique: jest.fn(),
      update: jest.fn(),
    },
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        TransactionsService,
        { provide: DatabaseService, useValue: mockDatabaseService },
      ],
    }).compile();

    service = module.get<TransactionsService>(TransactionsService);
    databaseService = module.get<DatabaseService>(DatabaseService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('createTransaction', () => {
    it('should create a transaction successfully', async () => {
      const createTransactionDto: CreateTransactionDto = {
        transactionId: 'trans123',
        userId: 'user123',
        externalUserId: 'ext123',
        qrCodeId: 'qr123',
        transactionType: 'STATIC',
        status: 'success',
        amount: 100,
        currency: 'USD',
        companyName: 'TestCo',
        redirectUrl: 'http://redirect',
        returnUrl: 'http://return',
        createdAtUtc: '2025-07-03T05:57:45.916817Z',
        timezone: 'UTC',
      };

      const mockTransaction = { id: 'trans123', ...createTransactionDto };
      mockDatabaseService.transaction.create.mockResolvedValue(mockTransaction);

      const result = await service.createTransaction(createTransactionDto);
      expect(result).toEqual(mockTransaction);
      expect(mockDatabaseService.transaction.create).toHaveBeenCalledWith({
        data: createTransactionDto,
      });
    });
  });

  describe('getAllTransactions', () => {
    it('should throw BadRequestException if page is invalid', async () => {
      await expect(service.getAllTransactions('user123', 0)).rejects.toThrow(
        new BadRequestException({
          statusCode: 400,
          message: 'Page number: 0. Page must be at least 1.',
          error: 'Bad Request',
        }),
      );

      await expect(service.getAllTransactions('user123', NaN)).rejects.toThrow(
        new BadRequestException({
          statusCode: 400,
          message: 'Page number: NaN. Page must be at least 1.',
          error: 'Bad Request',
        }),
      );
    });

    it('should throw BadRequestException if userId is missing', async () => {
      await expect(service.getAllTransactions('', 1)).rejects.toThrow(
        new BadRequestException({
          statusCode: 400,
          message: 'User ID is required.',
          error: 'Bad Request',
        }),
      );
    });

    it('should return paginated transactions with valid parameters', async () => {
      const mockTransactions = [
        { id: 'trans1', userId: 'user123', status: 'success', amount: 100 },
        { id: 'trans2', userId: 'user123', status: 'success', amount: 200 },
      ];
      mockDatabaseService.transaction.findMany.mockResolvedValue(
        mockTransactions,
      );
      mockDatabaseService.transaction.count.mockResolvedValue(2);

      const result = await service.getAllTransactions(
        'user123',
        1,
        20,
        '2025-01-01',
        '2025-12-31',
        'success',
      );

      expect(result).toEqual({
        totalCount: 2,
        page: 1,
        perPage: 20,
        totalPages: 1,
        transactions: mockTransactions,
      });

      expect(mockDatabaseService.transaction.findMany).toHaveBeenCalledWith({
        where: {
          userId: 'user123',
          createdAt: {
            gte: expect.any(Date),
            lte: expect.any(Date),
          },
          status: 'success',
        },
        skip: 0,
        take: 20,
        orderBy: { createdAt: 'desc' },
      });
    });

    it('should filter by default statuses when status is not provided', async () => {
      const mockTransactions = [
        { id: 'trans1', userId: 'user123', status: 'success', amount: 100 },
      ];
      mockDatabaseService.transaction.findMany.mockResolvedValue(
        mockTransactions,
      );
      mockDatabaseService.transaction.count.mockResolvedValue(1);

      const result = await service.getAllTransactions('user123', 1);

      expect(result).toEqual({
        totalCount: 1,
        page: 1,
        perPage: 20,
        totalPages: 1,
        transactions: mockTransactions,
      });

      expect(mockDatabaseService.transaction.findMany).toHaveBeenCalledWith({
        where: {
          userId: 'user123',
          status: {
            in: ['success', 'failed'],
          },
        },
        skip: 0,
        take: 20,
        orderBy: { createdAt: 'desc' },
      });
    });
  });

  describe('getTransactionById', () => {
    it('should throw NotFoundException if transaction is not found', async () => {
      mockDatabaseService.transaction.findUnique.mockResolvedValue(null);

      await expect(service.getTransactionById('trans123')).rejects.toThrow(
        new NotFoundException('Transaction not found'),
      );
    });

    it('should return transaction if found', async () => {
      const mockTransaction = {
        id: 'trans123',
        userId: 'user123',
        amount: 100,
      };
      mockDatabaseService.transaction.findUnique.mockResolvedValue(
        mockTransaction,
      );

      const result = await service.getTransactionById('trans123');
      expect(result).toEqual(mockTransaction);
      expect(mockDatabaseService.transaction.findUnique).toHaveBeenCalledWith({
        where: { id: 'trans123' },
      });
    });
  });

  describe('updateTransaction', () => {
    it('should throw NotFoundException if transaction is not found', async () => {
      mockDatabaseService.transaction.findUnique.mockResolvedValue(null);

      await expect(
        service.updateTransaction('trans123', { status: 'success' }),
      ).rejects.toThrow(
        new NotFoundException('Transaction with ID trans123 not found'),
      );
    });

    it('should update transaction successfully', async () => {
      const mockTransaction = {
        id: 'trans123',
        userId: 'user123',
        status: 'pending',
      };
      const updateDto: UpdateTransactionDto = { status: 'success' };
      const updatedTransaction = { ...mockTransaction, ...updateDto };

      mockDatabaseService.transaction.findUnique.mockResolvedValue(
        mockTransaction,
      );
      mockDatabaseService.transaction.update.mockResolvedValue(
        updatedTransaction,
      );

      const result = await service.updateTransaction('trans123', updateDto);
      expect(result).toEqual(updatedTransaction);
      expect(mockDatabaseService.transaction.update).toHaveBeenCalledWith({
        where: { id: 'trans123' },
        data: updateDto,
      });
    });
  });
});
