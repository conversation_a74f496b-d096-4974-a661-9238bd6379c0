import { Controller, Post, Patch, Get, Param, Query, Body, HttpCode } from '@nestjs/common';
import { TransactionsService } from './transactions.service';
import { CreateTransactionDto } from '../dto/create-transaction.dto';
import { UpdateTransactionDto } from '../dto/update-transaction.dto';

@Controller('transactions')
export class TransactionsController {
  constructor(private readonly transactionsService: TransactionsService) {}

  @Post()
  async createTransaction(@Body() dto: CreateTransactionDto) {
    return await this.transactionsService.createTransaction(dto);
  }

  @Get()
  @HttpCode(200)
  async getAllTransactions(
    @Query('userId') userId: string,
    @Query('page') page?: string,
    @Query('perPage') perPage?: string,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
    @Query('status') status?: string,
  ) {

    return this.transactionsService.getAllTransactions(
    userId,
    Number(page), 
    perPage ? Number(perPage) : 20,
    startDate, 
    endDate,
    status
  );
  }

  @Get(':id')
  @HttpCode(200)
  async getTransactionById(@Param('id') id: string) {
    return await this.transactionsService.getTransactionById(id);
  }

  @Patch(':id')
  @HttpCode(200)
  async updateTransaction(@Param('id') id: string, @Body() dto: UpdateTransactionDto) {
    return await this.transactionsService.updateTransaction(id, dto);
  }
}

