import { Test, TestingModule } from '@nestjs/testing';
import { TransactionsController } from './transactions.controller';
import { TransactionsService } from './transactions.service';

describe('TransactionsController', () => {
  let controller: TransactionsController;
  let service: TransactionsService;

  const mockTransactionsService = {
    createTransaction: jest.fn(),
    getAllTransactions: jest.fn(),
    getTransactionById: jest.fn(),
    updateTransaction: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [TransactionsController],
      providers: [
        { provide: TransactionsService, useValue: mockTransactionsService },
      ],
    }).compile();

    controller = module.get<TransactionsController>(TransactionsController);
    service = module.get<TransactionsService>(TransactionsService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('createTransaction', () => {
    it('should create a transaction and return it', async () => {
      const dto = {
        transactionId: 'tx1',
        userId: 'user1',
        externalUserId: 'ext_user1',
        qrCodeId: 'qr1',
        transactionType: 'STATIC',
        status: 'created',
        amount: 100,
        currency: 'EUR',
        companyName: 'Test Company',
        redirectUrl: 'https://example.com/redirect',
        returnUrl: 'https://example.com/return',
        createdAtUtc: '2025-07-03T05:57:45.916817Z',
        timezone: 'UTC',
      };
      const createdTransaction = { id: 'tx1', ...dto };

      mockTransactionsService.createTransaction.mockResolvedValue(
        createdTransaction,
      );

      await expect(controller.createTransaction(dto)).resolves.toEqual(
        createdTransaction,
      );
      expect(mockTransactionsService.createTransaction).toHaveBeenCalledWith(
        dto,
      );
    });
  });

  describe('getAllTransactions', () => {
    it('should return transactions with default perPage', async () => {
      const userId = 'user1';
      const mockResult = [{ id: 'tx1' }, { id: 'tx2' }];

      mockTransactionsService.getAllTransactions.mockResolvedValue(mockResult);

      await expect(
        controller.getAllTransactions(
          userId,
          '1',
          undefined,
          '2023-01-01',
          '2023-01-31',
          'completed',
        ),
      ).resolves.toEqual(mockResult);

      expect(mockTransactionsService.getAllTransactions).toHaveBeenCalledWith(
        userId,
        1,
        20,
        '2023-01-01',
        '2023-01-31',
        'completed',
      );
    });

    it('should parse perPage query param correctly', async () => {
      const userId = 'user2';
      const mockResult = [];

      mockTransactionsService.getAllTransactions.mockResolvedValue(mockResult);

      await controller.getAllTransactions(userId, '2', '10');

      expect(mockTransactionsService.getAllTransactions).toHaveBeenCalledWith(
        userId,
        2,
        10,
        undefined,
        undefined,
        undefined,
      );
    });
  });

  describe('getTransactionById', () => {
    it('should return a transaction by id', async () => {
      const id = 'tx123';
      const mockTransaction = { id, amount: 50 };

      mockTransactionsService.getTransactionById.mockResolvedValue(
        mockTransaction,
      );

      await expect(controller.getTransactionById(id)).resolves.toEqual(
        mockTransaction,
      );
      expect(mockTransactionsService.getTransactionById).toHaveBeenCalledWith(
        id,
      );
    });
  });

  describe('updateTransaction', () => {
    it('should update and return the updated transaction', async () => {
      const id = 'tx123';
      const dto = { status: 'completed' };
      const updatedTransaction = { id, status: 'completed' };

      mockTransactionsService.updateTransaction.mockResolvedValue(
        updatedTransaction,
      );

      await expect(controller.updateTransaction(id, dto)).resolves.toEqual(
        updatedTransaction,
      );
      expect(mockTransactionsService.updateTransaction).toHaveBeenCalledWith(
        id,
        dto,
      );
    });
  });
});
