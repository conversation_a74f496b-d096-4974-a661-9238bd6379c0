import { Test, TestingModule } from '@nestjs/testing';
import { QRCodeController } from './qrcode.controller';
import { QRCodeService } from './qrcode.service';
import { HttpException } from '@nestjs/common';

describe('QRCodeController', () => {
  let controller: QRCodeController;
  let service: QRCodeService;

  const mockQRCodeService = {
    getExternalQrCode: jest.fn(),
    updateQrCodeStatus: jest.fn(),
    createStaticQrPayment: jest.fn(),
    createPublicStaticQrPayment: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [QRCodeController],
      providers: [
        { provide: QRCodeService, useValue: mockQRCodeService },
      ],
    }).compile();

    controller = module.get<QRCodeController>(QRCodeController);
    service = module.get<QRCodeService>(QRCodeService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('getExternalQrCode', () => {
    it('should return QR code data on success', async () => {
      const qrcodeId = 'abc123';
      const mockResult = { id: qrcodeId, data: 'some data' };
      mockQRCodeService.getExternalQrCode.mockResolvedValue(mockResult);

      await expect(controller.getExternalQrCode(qrcodeId)).resolves.toEqual(mockResult);
      expect(mockQRCodeService.getExternalQrCode).toHaveBeenCalledWith(qrcodeId);
    });

    it('should throw HttpException on error', async () => {
      const qrcodeId = 'abc123';
      const error = { response: { data: { message: 'Not found' }, status: 404 } };
      mockQRCodeService.getExternalQrCode.mockRejectedValue(error);

      await expect(controller.getExternalQrCode(qrcodeId)).rejects.toThrow(HttpException);
    });
  });

  describe('updateQrCodeStatus', () => {
    it('should update status and return result on success', async () => {
      const qrcodeId = 'qr123';
      const status = 'active';
      const externalUserId = 'user1';
      const mockResult = { success: true };

      mockQRCodeService.updateQrCodeStatus.mockResolvedValue(mockResult);

      const request = { headers: { 'external-user-id': externalUserId } };
      const result = await controller.updateQrCodeStatus(qrcodeId, status, request);

      expect(mockQRCodeService.updateQrCodeStatus).toHaveBeenCalledWith(qrcodeId, status, externalUserId);
      expect(result).toEqual(mockResult);
    });

    it('should throw HttpException on error', async () => {
      const error = { response: { data: { message: 'Failed' }, status: 400 } };
      mockQRCodeService.updateQrCodeStatus.mockRejectedValue(error);

      const request = { headers: { 'external-user-id': 'user1' } };

      await expect(controller.updateQrCodeStatus('id', 'status', request)).rejects.toThrow(HttpException);
    });
  });

  describe('createStaticQrPayment', () => {
    it('should throw HttpException if userId missing', async () => {
      const request = { user: {} };
      await expect(controller.createStaticQrPayment('id', {
        amount: 1.00,
        currencyCode: 'USD'
      }, request)).rejects.toThrow(HttpException);
    });

    it('should create static QR payment and return result', async () => {
      const staticQRId = 'static123';
      const body = { amount: 100, currencyCode: 'USD' };
      const userId = 'user1';
      const request = { user: { userId } };
      const mockResult = { success: true };

      mockQRCodeService.createStaticQrPayment.mockResolvedValue(mockResult);

      const result = await controller.createStaticQrPayment(staticQRId, body, request);

      expect(mockQRCodeService.createStaticQrPayment).toHaveBeenCalledWith(staticQRId, body, userId);
      expect(result).toEqual(mockResult);
    });

    it('should throw HttpException on error', async () => {
      const error = { response: { data: { message: 'Failed' }, status: 400 } };
      mockQRCodeService.createStaticQrPayment.mockRejectedValue(error);

      const request = { user: { userId: 'user1' } };

      await expect(controller.createStaticQrPayment('id', {
        amount: 2.50,
        currencyCode: 'EUR'
      }, request)).rejects.toThrow(HttpException);
    });
  });

  describe('createPublicStaticQrPayment', () => {
  it('should create public static QR payment and return result', async () => {
    const body = {
      userName: 'John Doe',
      userEmail: '<EMAIL>',
      staticQRId: 'abc123',
      currency: 'USD',
      amount: 50,
    };
    const mockResult = { success: true };

    mockQRCodeService.createPublicStaticQrPayment.mockResolvedValue(mockResult);

    const result = await controller.createPublicStaticQrPayment(body);

    expect(mockQRCodeService.createPublicStaticQrPayment).toHaveBeenCalledWith(body);
    expect(result).toEqual(mockResult);
  });

  it('should throw HttpException on error', async () => {
    const error = { response: { data: { message: 'Failed' }, status: 400 } };
    mockQRCodeService.createPublicStaticQrPayment.mockRejectedValue(error);

    const body = {
      userName: 'John Doe',
      userEmail: '<EMAIL>',
      staticQRId: 'abc123',
      currency: 'USD',
      amount: 50,
    };

    await expect(controller.createPublicStaticQrPayment(body)).rejects.toThrow(HttpException);
  });
});
});
