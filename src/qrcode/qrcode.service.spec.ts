import { Test, TestingModule } from '@nestjs/testing';
import { QRCodeService } from './qrcode.service';
import { ConfigService } from '@nestjs/config';
import { HttpService } from '@nestjs/axios';
import { DatabaseService } from '../database/database.service';
import { of } from 'rxjs';
import { HttpException, HttpStatus } from '@nestjs/common';
import { QRCodeStatus } from './enums/qrcode.enum';
import * as bcrypt from 'bcrypt';

jest.mock('bcrypt');

describe('QRCodeService', () => {
  let service: QRCodeService;
  let httpService: HttpService;
  let configService: ConfigService;
  let databaseService: DatabaseService;

  const mockConfigService = {
    get: jest.fn(),
  };

  const mockHttpService = {
    get: jest.fn(),
    post: jest.fn(),
    patch: jest.fn(),
  };

  const mockDatabaseService = {
    user: {
      findUnique: jest.fn(),
      create: jest.fn(),
    },
    transaction: {
      create: jest.fn(),
    },
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        QRCodeService,
        { provide: ConfigService, useValue: mockConfigService },
        { provide: HttpService, useValue: mockHttpService },
        { provide: DatabaseService, useValue: mockDatabaseService },
      ],
    }).compile();

    service = module.get<QRCodeService>(QRCodeService);
    httpService = module.get<HttpService>(HttpService);
    configService = module.get<ConfigService>(ConfigService);
    databaseService = module.get<DatabaseService>(DatabaseService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('getExternalQrCode', () => {
    it('should throw if API credentials are not configured', async () => {
      mockConfigService.get.mockReturnValue(null);

      await expect(service.getExternalQrCode('qr123')).rejects.toThrow(
        new HttpException('API credentials not configured', HttpStatus.INTERNAL_SERVER_ERROR),
      );
    });

    it('should return REDEEMED message for redeemed QR code', async () => {
      mockConfigService.get
        .mockReturnValueOnce('apiKey')
        .mockReturnValueOnce('secretKey')
        .mockReturnValueOnce('http://qrcode-api')
        .mockReturnValueOnce('http://transaction-api');

      mockHttpService.get.mockReturnValueOnce(
        of({
          data: { data: { status: QRCodeStatus.REDEEMED } },
        }),
      );

      const result = await service.getExternalQrCode('qr123');
      expect(result).toEqual({ message: 'QR Code already scanned' });
    });

    it('should return transaction data for valid QR code with transactionId', async () => {
      mockConfigService.get
        .mockReturnValueOnce('apiKey')
        .mockReturnValueOnce('secretKey')
        .mockReturnValueOnce('http://qrcode-api')
        .mockReturnValueOnce('http://transaction-api');

      mockHttpService.get
        .mockReturnValueOnce(
          of({
            data: { data: { status: QRCodeStatus.SCANNED, transactionId: 'trans123' } },
          }),
        )
        .mockReturnValueOnce(
          of({
            data: { transaction: 'some-transaction-data' },
          }),
        );

      const result = await service.getExternalQrCode('qr123');
      expect(result).toEqual({ transaction: { transaction: 'some-transaction-data' } });
    });
  });

  describe('updateQrCodeStatus', () => {
    it('should throw if qrcodeId or status is missing', async () => {
      await expect(service.updateQrCodeStatus('', 'SCANNED', 'user123')).rejects.toThrow(
        new HttpException('qrcodeId and status are required', HttpStatus.BAD_REQUEST),
      );
    });

    it('should throw if API credentials are not configured', async () => {
      mockConfigService.get.mockReturnValue(null);

      await expect(service.updateQrCodeStatus('qr123', 'SCANNED', 'user123')).rejects.toThrow(
        new HttpException('API credentials not configured', HttpStatus.INTERNAL_SERVER_ERROR),
      );
    });

    it('should throw if externalUserId is missing', async () => {
      mockConfigService.get
        .mockReturnValueOnce('apiKey')
        .mockReturnValueOnce('secretKey')
        .mockReturnValueOnce('http://qrcode-status-api');

      await expect(service.updateQrCodeStatus('qr123', 'SCANNED', '')).rejects.toThrow(
        new HttpException('Missing required header: external-user-id', HttpStatus.BAD_REQUEST),
      );
    });

    it('should update QR code status successfully', async () => {
      mockConfigService.get
        .mockReturnValueOnce('apiKey')
        .mockReturnValueOnce('secretKey')
        .mockReturnValueOnce('http://qrcode-status-api');

      mockHttpService.patch.mockReturnValueOnce(of({ data: {} }));

      const result = await service.updateQrCodeStatus('qr123', QRCodeStatus.SCANNED, 'user123');
      expect(result).toEqual({ message: 'QR Code status updated successfully' });
      expect(service['qrCodeData']['qr123']).toEqual({
        id: 'qr123',
        status: QRCodeStatus.SCANNED,
      });
    });
  });

  describe('getQrCode', () => {
    it('should return null for non-existent QR code', async () => {
      const result = await service.getQrCode('nonexistent');
      expect(result).toBeNull();
    });

    it('should return QR code data for existing QR code', async () => {
      service['qrCodeData']['qr123'] = { id: 'qr123', status: QRCodeStatus.CREATED };
      const result = await service.getQrCode('qr123');
      expect(result).toEqual({ id: 'qr123', status: QRCodeStatus.CREATED });
    });
  });

  describe('createStaticQrPayment', () => {
    it('should throw if API credentials are not configured', async () => {
      mockConfigService.get.mockReturnValue(null);

      await expect(
        service.createStaticQrPayment('static123', { amount: 100, currencyCode: 'USD' }, 'user123'),
      ).rejects.toThrow(
        new HttpException('API credentials not configured', HttpStatus.INTERNAL_SERVER_ERROR),
      );
    });

    it('should create static QR payment successfully', async () => {
      mockConfigService.get
        .mockReturnValueOnce('http://create-payment-api')
        .mockReturnValueOnce('http://transaction-details-api');

      mockHttpService.post.mockReturnValueOnce(
        of({ data: { data: 'trans123' } }),
      );
      mockHttpService.get.mockReturnValueOnce(
        of({
          data: {
            data: {
              id: 'trans123',
              userId: 'user123',
              status: 'PENDING',
              amount: 10000,
              currency: 'USD',
              company: 'TestCo',
              redirectUrl: 'http://redirect',
              returnUrl: 'http://return',
            },
          },
        }),
      );

      mockDatabaseService.transaction.create.mockResolvedValue({
        id: 'db-trans123',
        transactionId: 'trans123',
        userId: 'user123',
        externalUserId: 'user123',
        status: 'PENDING',
        amount: 100,
        currency: 'USD',
        companyName: 'TestCo',
        redirectUrl: 'http://redirect',
        returnUrl: 'http://return',
        updatedAt: new Date(),
      });

      const result = await service.createStaticQrPayment(
        'static123',
        { amount: 100, currencyCode: 'USD' },
        'user123',
      );

      expect(result).toMatchObject({
        status: true,
        message: 'Transaction stored successfully',
        data: {
          paymentId: 'trans123',
          transactionId: 'db-trans123',
          amount: 100,
          currency: 'USD',
          company: 'TestCo',
        },
      });
    });
  });

  // describe('createPublicStaticQrPayment', () => {
  //   it('should throw if API credentials are not configured', async () => {
  //     mockConfigService.get.mockReturnValue(null);

  //     await expect(
  //       service.createPublicStaticQrPayment({
  //         userEmail: '<EMAIL>',
  //         userName: 'Test User',
  //         amount: 100,
  //         currency: 'USD',
  //         staticQRId: 'static123',
  //       }),
  //     ).rejects.toThrow(
  //       new HttpException('API credentials not configured', HttpStatus.INTERNAL_SERVER_ERROR),
  //     );
  //   });

  //   it('should create new user and payment for non-existing user', async () => {
  //     mockConfigService.get
  //       .mockReturnValueOnce('http://ecommerce-api')
  //       .mockReturnValueOnce('http://transaction-api');

  //     mockDatabaseService.user.findUnique.mockResolvedValue(null);
  //     (bcrypt.hash as jest.Mock).mockResolvedValue('hashed-password');
  //     mockDatabaseService.user.create.mockResolvedValue({
  //       id: 'user123',
  //       email: '<EMAIL>',
  //       name: 'Test User',
  //     });

  //     mockHttpService.post.mockReturnValueOnce(
  //       of({ data: { data: 'trans123' } }),
  //     );
  //     mockHttpService.get.mockReturnValueOnce(
  //       of({
  //         data: {
  //           data: {
  //             id: 'trans123',
  //             userId: 'user123',
  //             status: 'PENDING',
  //             amount: 10000,
  //             currency: 'USD',
  //             company: 'TestCo',
  //             redirectUrl: 'http://redirect',
  //             returnUrl: 'http://return',
  //           },
  //         },
  //       }),
  //     );

  //     mockDatabaseService.transaction.create.mockResolvedValue({
  //       id: 'db-trans123',
  //       transactionId: 'trans123',
  //     });

  //     const result = await service.createPublicStaticQrPayment({
  //       userEmail: '<EMAIL>',
  //       userName: 'Test User',
  //       amount: 100,
  //       currency: 'USD',
  //       staticQRId: 'static123',
  //     });

  //     expect(result).toEqual({
  //       status: true,
  //       message: 'Payment created successfully',
  //       data: 'trans123',
  //     });
  //     expect(mockDatabaseService.user.create).toHaveBeenCalled();
  //   });
  // });
});