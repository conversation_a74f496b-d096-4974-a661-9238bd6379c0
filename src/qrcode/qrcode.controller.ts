import {
  <PERSON>,
  Get,
  Patch,
  Param,
  Post,
  Body,
  UseGuards,
  HttpException,
  HttpStatus,
  Query,
  Req,
  HttpCode,
  Res,
} from '@nestjs/common';
import { Response } from 'express';
import { QRCodeService } from './qrcode.service';
import { AuthGuard } from '@nestjs/passport';
import { CreateStaticPaymentDto } from 'src/dto/create-static-payment.dto';
import { PublicStaticPaymentDto } from 'src/dto/public-static-payment.dto';
import { DynamicQRPaymentDto } from 'src/dto/dynamic-qr-payment.dto';

@Controller('qrcodes')
export class QRCodeController {
  constructor(private readonly qrCodeService: QRCodeService) { }

  @UseGuards(AuthGuard('jwt'))
  @Get('external/:qrcodeId')
  @HttpCode(200)
  async getExternalQrCode(@Param('qrcodeId') qrcodeId: string) {
    try {
      return await this.qrCodeService.getExternalQrCode(qrcodeId);
    } catch (error) {
      throw new HttpException(
        error.response?.data?.message || 'Failed to retrieve external QR Code',
        error.response?.status || HttpStatus.BAD_REQUEST,
      );
    }
  }

  @UseGuards(AuthGuard('jwt'))
  @Patch('online/public/qrcode-status')
  @HttpCode(200)
  async updateQrCodeStatus(
    @Query('qrcodeId') qrcodeId: string,
    @Query('status') status: string,
    @Req() request: any,
  ) {
    try {
      const externalUserId = request.headers['external-user-id'] as string;
      return await this.qrCodeService.updateQrCodeStatus(
        qrcodeId,
        status,
        externalUserId,
      );
    } catch (error) {
      throw new HttpException(
        error.response?.data?.message || 'Failed to update QR Code status',
        error.response?.status || HttpStatus.BAD_REQUEST,
      );
    }
  }

  @UseGuards(AuthGuard('jwt'))
  @Post('static-payment/:staticQRId')
  @HttpCode(201)
  async createStaticQrPayment(
    @Param('staticQRId') staticQRId: string,
    @Body() body: CreateStaticPaymentDto,
    @Req() request: any,
  ) {
    const userId = request.user?.userId;
    if (!userId) {
      throw new HttpException('Missing user ID', HttpStatus.UNAUTHORIZED);
    }
    try {
      return await this.qrCodeService.createStaticQrPayment(
        staticQRId,
        body,
        userId,
      );
    } catch (error) {
      throw new HttpException(
        error.response?.data?.message ||
        'Failed to create static QR code payment',
        error.response?.status || HttpStatus.BAD_REQUEST,
      );
    }
  }

  @Post('public/dynamic-qr-payment')
  @HttpCode(201)
  async dynamicQrPayment(@Body() body: DynamicQRPaymentDto) {
    try {
      return await this.qrCodeService.dynamicQrPayment(body);
    } catch (error) {
      throw new HttpException(
        error.response?.data?.message ||
        'Failed to fetch dynamic QR code payment details',
        error.response?.status || HttpStatus.BAD_REQUEST,
      );
    }
  }

  @Post('public/create-static-qr-payment')
  @HttpCode(201)
  async createPublicStaticQrPayment(@Body() body: PublicStaticPaymentDto) {
    try {
      return await this.qrCodeService.createPublicStaticQrPayment(body);
    } catch (error) {
      throw new HttpException(
        error.response?.data?.message ||
        'Failed to create public static QR code payment',
        error.response?.status || HttpStatus.BAD_REQUEST,
      );
    }
  }

  @Get('public/checkout-status')
  async checkoutStatus(@Query('transactionId') transactionId: string, @Res() res: Response) {
    if (!transactionId) {
      throw new HttpException(
        'Transaction ID is required',
        HttpStatus.BAD_REQUEST,
      );
    }

    try {
      const status = await this.qrCodeService.checkoutStatus(transactionId);

      let redirectUrl = 'https://hazelsone.com/return?status=unknown';
      if (status === 'AUTHORISED') {
        redirectUrl = 'https://hazelsone.com/return?status=success';
      } else if (status === 'CANCELLED') {
        redirectUrl = 'https://hazelsone.com/return?status=failed';
      }

      return res.redirect(302, redirectUrl);

    } catch (error) {
      throw new HttpException(
        error.response?.data?.message || 'Failed to process checkout status',
        error.response?.status || HttpStatus.BAD_REQUEST,
      );
    }
  }
}
