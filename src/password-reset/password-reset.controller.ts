import { <PERSON>, Post, Body, BadRequestException, HttpCode } from '@nestjs/common';
import { PasswordResetService } from './password-reset.service';

@Controller('password-reset')
export class PasswordResetController {
  constructor(private passwordResetService: PasswordResetService) {}

  @Post('initiate')
  @HttpCode(200)
  async initiatePasswordReset(@Body('email') email: string) {
    if (!email) {
      throw new BadRequestException('Email is required');
    }
    await this.passwordResetService.initiatePasswordReset(email);
    
    return {
      status: 'true',
      message: 'Password reset email sent',
      data: null,
    };
  }

  @Post('reset')
  @HttpCode(200)
  async resetPassword(
    @Body('token') token: string,
    @Body('password') password: string,
  ) {
    if (!token || !password) {
      throw new BadRequestException('Token, and password are required');
    }
    await this.passwordResetService.resetPassword(token, password);

    return {
      status: 'true',
      message: 'Password reset successfully',
      data: null,
    };
  }
}