import { Test, TestingModule } from '@nestjs/testing';
import { PasswordResetController } from './password-reset.controller';
import { PasswordResetService } from './password-reset.service';
import { BadRequestException } from '@nestjs/common';

describe('PasswordResetController', () => {
  let controller: PasswordResetController;
  let service: PasswordResetService;

  const mockPasswordResetService = {
    initiatePasswordReset: jest.fn(),
    resetPassword: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [PasswordResetController],
      providers: [
        { provide: PasswordResetService, useValue: mockPasswordResetService },
      ],
    }).compile();

    controller = module.get<PasswordResetController>(PasswordResetController);
    service = module.get<PasswordResetService>(PasswordResetService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('initiatePasswordReset', () => {
    it('should throw BadRequestException if email is missing', async () => {
      await expect(controller.initiatePasswordReset('')).rejects.toThrow(BadRequestException);
    });

    it('should call service.initiatePasswordReset and return success response', async () => {
      mockPasswordResetService.initiatePasswordReset.mockResolvedValue(undefined);

      const result = await controller.initiatePasswordReset('<EMAIL>');

      expect(mockPasswordResetService.initiatePasswordReset).toHaveBeenCalledWith('<EMAIL>');
      expect(result).toEqual({
        status: 'true',
        message: 'Password reset email sent',
        data: null,
      });
    });
  });

  describe('resetPassword', () => {
    it('should throw BadRequestException if token or password is missing', async () => {
      await expect(controller.resetPassword('', 'newpassword')).rejects.toThrow(BadRequestException);
      await expect(controller.resetPassword('token', '')).rejects.toThrow(BadRequestException);
      await expect(controller.resetPassword('', '')).rejects.toThrow(BadRequestException);
    });

    it('should call service.resetPassword and return success response', async () => {
      mockPasswordResetService.resetPassword.mockResolvedValue(undefined);

      const result = await controller.resetPassword('token123', 'newpassword');

      expect(mockPasswordResetService.resetPassword).toHaveBeenCalledWith('token123', 'newpassword');
      expect(result).toEqual({
        status: 'true',
        message: 'Password reset successfully',
        data: null,
      });
    });
  });
});
