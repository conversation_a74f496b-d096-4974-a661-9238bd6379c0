import { Test, TestingModule } from '@nestjs/testing';
import { PasswordResetService } from './password-reset.service';
import { DatabaseService } from '../database/database.service';
import { EmailService } from '../email/email.service';
import { ConfigService } from '@nestjs/config';
import { NotFoundException, BadRequestException } from '@nestjs/common';
import * as bcrypt from 'bcrypt';
import * as crypto from 'crypto';

jest.mock('bcrypt');
jest.mock('crypto');

describe('PasswordResetService', () => {
  let service: PasswordResetService;
  let databaseService: DatabaseService;
  let emailService: EmailService;
  let configService: ConfigService;

  const mockDatabaseService = {
    user: {
      findUnique: jest.fn(),
      findMany: jest.fn(),
      update: jest.fn(),
    },
  };

  const mockEmailService = {
    sendReceipt: jest.fn(),
  };

  const mockConfigService = {
    get: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        PasswordResetService,
        { provide: DatabaseService, useValue: mockDatabaseService },
        { provide: EmailService, useValue: mockEmailService },
        { provide: ConfigService, useValue: mockConfigService },
      ],
    }).compile();

    service = module.get<PasswordResetService>(PasswordResetService);
    databaseService = module.get<DatabaseService>(DatabaseService);
    emailService = module.get<EmailService>(EmailService);
    configService = module.get<ConfigService>(ConfigService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('initiatePasswordReset', () => {
    it('should throw NotFoundException if user is not found', async () => {
      mockDatabaseService.user.findUnique.mockResolvedValue(null);

      await expect(service.initiatePasswordReset('<EMAIL>')).rejects.toThrow(
        new NotFoundException('User not found'),
      );

      expect(mockDatabaseService.user.findUnique).toHaveBeenCalledWith({
        where: { email: '<EMAIL>' },
      });
      expect(mockDatabaseService.user.update).not.toHaveBeenCalled();
      expect(mockEmailService.sendReceipt).not.toHaveBeenCalled();
    });

    it('should initiate password reset successfully', async () => {
      const mockUser = { id: 'user123', email: '<EMAIL>' };
      const mockResetToken = 'randomtoken123';
      const mockHashedToken = 'hashedtoken123';
      const resetBaseUrl = 'http://example.com/reset';

      mockDatabaseService.user.findUnique.mockResolvedValue(mockUser);
      (crypto.randomBytes as jest.Mock).mockReturnValue({ toString: () => mockResetToken });
      (bcrypt.hash as jest.Mock).mockResolvedValue(mockHashedToken);
      mockConfigService.get.mockReturnValue(resetBaseUrl);
      mockDatabaseService.user.update.mockResolvedValue(mockUser);
      mockEmailService.sendReceipt.mockResolvedValue(undefined);

      await service.initiatePasswordReset('<EMAIL>');

      expect(mockDatabaseService.user.findUnique).toHaveBeenCalledWith({
        where: { email: '<EMAIL>' },
      });
      expect(crypto.randomBytes).toHaveBeenCalledWith(32);
      expect(bcrypt.hash).toHaveBeenCalledWith(mockResetToken, 6);
      expect(mockDatabaseService.user.update).toHaveBeenCalledWith({
        where: { email: '<EMAIL>' },
        data: { resetToken: mockHashedToken },
      });
      expect(mockConfigService.get).toHaveBeenCalledWith('RESET_PASSWORD_PREPROD_URL');
      expect(mockEmailService.sendReceipt).toHaveBeenCalledWith(
        '<EMAIL>',
        'Password Reset Request',
        expect.stringContaining(`<a href="${resetBaseUrl}/${mockResetToken}"`),
      );
    });
  });

  describe('resetPassword', () => {
    it('should throw BadRequestException if token is invalid', async () => {
      mockDatabaseService.user.findMany.mockResolvedValue([
        { id: 'user123', resetToken: 'hashedtoken123' },
      ]);
      (bcrypt.compare as jest.Mock).mockResolvedValue(false);

      await expect(service.resetPassword('invalidtoken', 'newpassword')).rejects.toThrow(
        new BadRequestException('Invalid or expired reset token'),
      );

      expect(mockDatabaseService.user.findMany).toHaveBeenCalledWith({
        where: { resetToken: { not: null } },
        select: { id: true, resetToken: true },
      });
      expect(bcrypt.compare).toHaveBeenCalledWith('invalidtoken', 'hashedtoken123');
      expect(mockDatabaseService.user.update).not.toHaveBeenCalled();
    });

    it('should throw BadRequestException if no users with reset token exist', async () => {
      mockDatabaseService.user.findMany.mockResolvedValue([]);

      await expect(service.resetPassword('token123', 'newpassword')).rejects.toThrow(
        new BadRequestException('Invalid or expired reset token'),
      );

      expect(mockDatabaseService.user.findMany).toHaveBeenCalledWith({
        where: { resetToken: { not: null } },
        select: { id: true, resetToken: true },
      });
      expect(mockDatabaseService.user.update).not.toHaveBeenCalled();
    });

    it('should reset password successfully', async () => {
      const mockUser = { id: 'user123', resetToken: 'hashedtoken123' };
      const mockHashedPassword = 'hashedpassword123';

      mockDatabaseService.user.findMany.mockResolvedValue([mockUser]);
      (bcrypt.compare as jest.Mock).mockResolvedValue(true);
      (bcrypt.hash as jest.Mock).mockResolvedValue(mockHashedPassword);
      mockDatabaseService.user.update.mockResolvedValue({ id: 'user123', password: mockHashedPassword });

      await service.resetPassword('token123', 'newpassword');

      expect(mockDatabaseService.user.findMany).toHaveBeenCalledWith({
        where: { resetToken: { not: null } },
        select: { id: true, resetToken: true },
      });
      expect(bcrypt.compare).toHaveBeenCalledWith('token123', 'hashedtoken123');
      expect(bcrypt.hash).toHaveBeenCalledWith('newpassword', 6);
      expect(mockDatabaseService.user.update).toHaveBeenCalledWith({
        where: { id: 'user123' },
        data: {
          password: mockHashedPassword,
          resetToken: null,
        },
      });
    });
  });
});