import {
  Injectable,
  BadRequestException,
  NotFoundException,
} from '@nestjs/common';
import { DatabaseService } from '../database/database.service';
import * as bcrypt from 'bcrypt';
import * as crypto from 'crypto';
import { EmailService } from '../email/email.service';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class PasswordResetService {
  constructor(
    private databaseService: DatabaseService,
    private emailService: EmailService,
    private configService: ConfigService,
  ) {}

  async initiatePasswordReset(email: string): Promise<void> {
    const user = await this.databaseService.user.findUnique({
      where: { email },
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    const resetToken = crypto.randomBytes(32).toString('hex');
    const hashedToken = await bcrypt.hash(resetToken, 6);

    await this.databaseService.user.update({
      where: { email },
      data: {
        resetToken: hashedToken,
      },
    });

    const resetBaseUrl = this.configService.get<string>(
      'RESET_PASSWORD_PREPROD_URL',
    );
    const resetUrl = `${resetBaseUrl}/${resetToken}`;
    const htmlContent = `
     <p>Dear user,</p>
     <p>We received a request to reset your password. If you made this request, please click the link below to set a new password:</p>
     <p><a href="${resetUrl}" style="color: #007BFF; text-decoration: none;">Reset Your Password</a></p>
     <p>If you did not request a password reset, please ignore this email. Your password will remain unchanged.</p>
     <p>Thank you,<br>Hazel Pay Team</p>
     `;

    await this.emailService.sendReceipt(
      email,
      'Password Reset Request',
      htmlContent,
    );
  }

  async resetPassword(token: string, newPassword: string): Promise<void> {
    const users = await this.databaseService.user.findMany({
      where: {
        resetToken: {
          not: null,
        },
      },
      select: {
        id: true,
        resetToken: true,
      },
    });

    const user = await Promise.any(
      users.map(async (user) => {
        if (!user.resetToken) return Promise.reject();

        const isMatch = await bcrypt.compare(token, user.resetToken);
        return isMatch ? user : Promise.reject();
      }),
    ).catch(() => null);

    if (!user) {
      throw new BadRequestException('Invalid or expired reset token');
    }
  
    const hashedPassword = await bcrypt.hash(newPassword, 6);
  
    await this.databaseService.user.update({
      where: { id: user.id },
      data: {
        password: hashedPassword,
        resetToken: null,
      },
    });
  }
}
