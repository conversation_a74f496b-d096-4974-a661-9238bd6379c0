import { Modu<PERSON> } from '@nestjs/common';
import { PasswordResetService } from './password-reset.service';
import { PasswordResetController } from './password-reset.controller';
import { DatabaseService } from '../database/database.service';
import { EmailService } from '../email/email.service';
import { ConfigModule } from '@nestjs/config';

@Module({
  imports: [
    ConfigModule,
  ],
  controllers: [PasswordResetController],
  providers: [PasswordResetService, DatabaseService, EmailService],
})
export class PasswordResetModule {}