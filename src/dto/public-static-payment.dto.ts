import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, IsString } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class PublicStaticPaymentDto {
  @ApiProperty({ example: '<PERSON>', description: 'Name of the user' })
  @IsString()
  userName: string;

  @ApiProperty({ example: '<EMAIL>', description: 'Email of the user' })
  @IsEmail()
  userEmail: string;

  @ApiProperty({ example: 'abc123', description: 'Static QR code ID' })
  @IsString()
  staticQRId: string;

  @ApiProperty({ example: 'EUR', description: 'Currency code' })
  @IsString()
  currency: string;

  @ApiProperty({ example: 3200, description: 'Payment amount' })
  @IsNumber()
  amount: number;
}
