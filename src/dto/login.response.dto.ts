import { IsEmail, IsString } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class LoginResponseDto {
  @ApiProperty({ example: 'uuid', description: 'Unique user ID (UUID).' })
  id: string;

  @ApiProperty({ example: '<PERSON>', description: 'Full name of the user.' })
  name: string;

  @ApiProperty({
    example: '<EMAIL>',
    description: 'User email address.',
  })
  email: string;

  @ApiProperty({
    example: 'jwt-token',
    description: 'JWT token for authentication (Valid for 7 days).',
  })
  token: string;
}
