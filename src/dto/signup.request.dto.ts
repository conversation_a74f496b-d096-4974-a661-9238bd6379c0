import { ApiProperty } from '@nestjs/swagger';
import { IsEmail, IsString, MinLength } from 'class-validator';

export class SignupRequestDto {
  @ApiProperty({ example: '<PERSON>', description: 'Full name of the user' })
  @IsString()
  name: string;

  @ApiProperty({
    example: '<EMAIL>',
    description: 'Unique email address',
  })
  @IsEmail()
  email: string;

  @ApiProperty({
    example: 'securePassword123',
    description: 'Password for the user account',
    minimum: 6,
  })
  @IsString()
  @MinLength(6)
  password: string;
}
