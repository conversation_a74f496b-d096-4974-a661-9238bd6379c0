import { ApiProperty } from '@nestjs/swagger';
import { IsEmail, IsString, MinLength } from 'class-validator';

export class SignupResponseDto {
  @ApiProperty({ example: 'uuid', description: 'Unique user ID (UUID).' })
  id: string;

  @ApiProperty({ example: '<PERSON>', description: 'Full name of the user' })
  @IsString()
  name: string;

  @ApiProperty({
    example: '<EMAIL>',
    description: 'Unique email address',
  })
  @IsEmail()
  email: string;
}
