import { IsOptional, IsString, IsEmail } from 'class-validator';

export class UpdateUserInfoDto {
  @IsOptional()
  @IsString()
  name?: string;

  @IsOptional()
  @IsEmail()
  email?: string;

  @IsOptional()
  @IsString()
  number?: string;

  @IsOptional()
  @IsString() 
  billingAddress?: string;

  @IsOptional()
  @IsString() 
  billingPostal?: string;

  @IsOptional()
  @IsString() 
  billingCity?: string;

  @IsOptional() 
  @IsString() 
  billingCountry?: string;

  @IsOptional() 
  @IsString() 
  shippingAddress?: string;

  @IsOptional() 
  @IsString() 
  shippingPostal?: string;
  
  @IsOptional() 
  @IsString() 
  shippingCity?: string;

  @IsOptional()
  @IsString() 
  shippingCountry?: string;
 }