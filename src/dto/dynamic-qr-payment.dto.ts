import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, IsString } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class DynamicQRPaymentDto {
  @ApiProperty({ example: '<PERSON>', description: 'Name of the user' })
  @IsString()
  userName: string;

  @ApiProperty({
    example: '<EMAIL>',
    description: 'Email of the user',
  })
  @IsEmail()
  userEmail: string;

  @ApiProperty({ example: 'abc123', description: 'Dynamic QR code ID' })
  @IsString()
  dynamicQRId: string;
}
