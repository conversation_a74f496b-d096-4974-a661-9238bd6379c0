import { Body, Controller, HttpCode, Patch, Post, Req, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBody } from '@nestjs/swagger';
import { AuthService } from './auth.service';
import { SignupRequestDto } from 'src/dto/signup.request.dto';
import { LoginRequestDto } from 'src/dto/login.request.dto';
import { LoginResponseDto } from 'src/dto/login.response.dto';
import { SignupResponseDto } from 'src/dto/signup.response.dto';
import { ChangePasswordDto } from 'src/dto/change-password.dto';
import { AuthGuard } from '@nestjs/passport';

@ApiTags('Auth')
@Controller('auth')
export class AuthController {
  constructor(private readonly authService: AuthService) {}

  @Post('signup')
  @ApiOperation({ summary: 'Sign up a new user' })
  @ApiResponse({ status: 201, type: SignupResponseDto })
  @ApiResponse({ status: 409, description: 'Email already registered.' })
  @ApiBody({ type: SignupRequestDto })
  async signUp(@Body() SignupRequestDto: SignupRequestDto) {
    return this.authService.signUp(
      SignupRequestDto.name,
      SignupRequestDto.email,
      SignupRequestDto.password,
    );
  }

  @Post('login')
  @HttpCode(200)
  @ApiOperation({ summary: 'User login' })
  @ApiResponse({ status: 200, type: LoginResponseDto })
  @ApiResponse({ status: 401, description: 'Invalid credentials.' })
  @ApiBody({ type: LoginRequestDto })
  async login(@Body() LoginRequestDto: LoginRequestDto) {
    return this.authService.login(
      LoginRequestDto.email,
      LoginRequestDto.password,
    );
  }

  @UseGuards(AuthGuard('jwt'))
  @Patch('change-password')
  async changePassword( @Req() req, @Body() dto: ChangePasswordDto ) {
    const userId = req.user.userId;
    return this.authService.changePassword(userId, dto);
  }
}
