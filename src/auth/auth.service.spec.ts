import { Test, TestingModule } from '@nestjs/testing';
import { AuthService } from './auth.service';
import { DatabaseService } from '../database/database.service';
import { JwtService } from '@nestjs/jwt';
import { ConflictException, UnauthorizedException, NotFoundException, BadRequestException } from '@nestjs/common';
import * as bcrypt from 'bcrypt';
import { ChangePasswordDto } from '../dto/change-password.dto';

jest.mock('bcrypt', () => ({
  hash: jest.fn(),
  compare: jest.fn(),
}));

describe('AuthService', () => {
  let service: AuthService;
  let mockDatabaseService: any;
  let mockJwtService: any;

  beforeEach(async () => {
    mockDatabaseService = {
      user: {
        findUnique: jest.fn(),
        create: jest.fn(),
        update: jest.fn(),
      },
    };

    mockJwtService = {
      sign: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AuthService,
        { provide: DatabaseService, useValue: mockDatabaseService },
        { provide: JwtService, useValue: mockJwtService },
      ],
    }).compile();

    service = module.get<AuthService>(AuthService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('signUp', () => {
    const signUpInput = {
      name: 'John Doe',
      email: '<EMAIL>',
      password: 'password123',
    };

    it('should successfully sign up a new user', async () => {
      mockDatabaseService.user.findUnique.mockResolvedValue(null);
      (bcrypt.hash as jest.Mock).mockResolvedValue('hashedPassword');
      mockDatabaseService.user.create.mockResolvedValue({
        id: 'user1',
        name: signUpInput.name,
        email: signUpInput.email,
        password: 'hashedPassword',
      });

      const result = await service.signUp(signUpInput.name, signUpInput.email, signUpInput.password);

      expect(mockDatabaseService.user.findUnique).toHaveBeenCalledWith({ where: { email: signUpInput.email } });
      expect(bcrypt.hash).toHaveBeenCalledWith(signUpInput.password, 10);
      expect(mockDatabaseService.user.create).toHaveBeenCalledWith({
        data: {
          name: signUpInput.name,
          email: signUpInput.email,
          password: 'hashedPassword',
        },
      });
      expect(result).toEqual({
        status: true,
        message: 'User signed up successfully',
        data: {
          id: 'user1',
          name: signUpInput.name,
          email: signUpInput.email,
        },
      });
    });

    it('should throw ConflictException if email is already registered', async () => {
      mockDatabaseService.user.findUnique.mockResolvedValue({ id: 'user1', email: signUpInput.email });

      await expect(service.signUp(signUpInput.name, signUpInput.email, signUpInput.password))
        .rejects.toThrow(new ConflictException('Email already registered'));
      expect(mockDatabaseService.user.findUnique).toHaveBeenCalledWith({ where: { email: signUpInput.email } });
      expect(bcrypt.hash).not.toHaveBeenCalled();
      expect(mockDatabaseService.user.create).not.toHaveBeenCalled();
    });
  });

  describe('login', () => {
    const loginInput = {
      email: '<EMAIL>',
      password: 'password123',
    };

    it('should successfully login a user', async () => {
      const user = {
        id: 'user1',
        name: 'John Doe',
        email: loginInput.email,
        password: 'hashedPassword',
      };
      mockDatabaseService.user.findUnique.mockResolvedValue(user);
      (bcrypt.compare as jest.Mock).mockResolvedValue(true);
      mockJwtService.sign.mockReturnValue('jwtToken');

      const result = await service.login(loginInput.email, loginInput.password);

      expect(mockDatabaseService.user.findUnique).toHaveBeenCalledWith({ where: { email: loginInput.email } });
      expect(bcrypt.compare).toHaveBeenCalledWith(loginInput.password, user.password);
      expect(mockJwtService.sign).toHaveBeenCalledWith({ userId: user.id, email: user.email });
      expect(result).toEqual({
        status: true,
        message: 'Login successful',
        data: {
          id: user.id,
          name: user.name,
          email: user.email,
          token: 'jwtToken',
        },
      });
    });

    it('should throw UnauthorizedException if user does not exist', async () => {
      mockDatabaseService.user.findUnique.mockResolvedValue(null);

      await expect(service.login(loginInput.email, loginInput.password))
        .rejects.toThrow(new UnauthorizedException('Invalid credentials'));
      expect(mockDatabaseService.user.findUnique).toHaveBeenCalledWith({ where: { email: loginInput.email } });
      expect(bcrypt.compare).not.toHaveBeenCalled();
      expect(mockJwtService.sign).not.toHaveBeenCalled();
    });

    it('should throw UnauthorizedException if password is invalid', async () => {
      mockDatabaseService.user.findUnique.mockResolvedValue({
        id: 'user1',
        email: loginInput.email,
        password: 'hashedPassword',
      });
      (bcrypt.compare as jest.Mock).mockResolvedValue(false);

      await expect(service.login(loginInput.email, loginInput.password))
        .rejects.toThrow(new UnauthorizedException('Invalid credentials'));
      expect(mockDatabaseService.user.findUnique).toHaveBeenCalledWith({ where: { email: loginInput.email } });
      expect(bcrypt.compare).toHaveBeenCalledWith(loginInput.password, 'hashedPassword');
      expect(mockJwtService.sign).not.toHaveBeenCalled();
    });
  });

  describe('changePassword', () => {
    const userId = 'user1';
    const changePasswordDto: ChangePasswordDto = {
      oldPassword: 'oldPassword123',
      newPassword: 'newPassword123',
    };

    it('should successfully change the password', async () => {
      const user = { id: userId, password: 'hashedOldPassword' };
      mockDatabaseService.user.findUnique.mockResolvedValue(user);
      (bcrypt.compare as jest.Mock).mockResolvedValue(true);
      (bcrypt.hash as jest.Mock).mockResolvedValue('hashedNewPassword');
      mockDatabaseService.user.update.mockResolvedValue({ id: userId, password: 'hashedNewPassword' });

      const result = await service.changePassword(userId, changePasswordDto);

      expect(mockDatabaseService.user.findUnique).toHaveBeenCalledWith({ where: { id: userId } });
      expect(bcrypt.compare).toHaveBeenCalledWith(changePasswordDto.oldPassword, user.password);
      expect(bcrypt.hash).toHaveBeenCalledWith(changePasswordDto.newPassword, 10);
      expect(mockDatabaseService.user.update).toHaveBeenCalledWith({
        where: { id: userId },
        data: { password: 'hashedNewPassword' },
      });
      expect(result).toEqual({
        status: true,
        message: 'Password changed successfully',
        data: null,
      });
    });

    it('should throw NotFoundException if user does not exist', async () => {
    
      mockDatabaseService.user.findUnique.mockResolvedValue(null);

      await expect(service.changePassword(userId, changePasswordDto))
        .rejects.toThrow(new NotFoundException('User not found'));
      expect(mockDatabaseService.user.findUnique).toHaveBeenCalledWith({ where: { id: userId } });
      expect(bcrypt.compare).not.toHaveBeenCalled();
      expect(bcrypt.hash).not.toHaveBeenCalled();
      expect(mockDatabaseService.user.update).not.toHaveBeenCalled();
    });

    it('should throw BadRequestException if old password is incorrect', async () => {
  
      mockDatabaseService.user.findUnique.mockResolvedValue({ id: userId, password: 'hashedOldPassword' });
      (bcrypt.compare as jest.Mock).mockResolvedValue(false);

      await expect(service.changePassword(userId, changePasswordDto))
        .rejects.toThrow(new BadRequestException('Old password is incorrect'));
      expect(mockDatabaseService.user.findUnique).toHaveBeenCalledWith({ where: { id: userId } });
      expect(bcrypt.compare).toHaveBeenCalledWith(changePasswordDto.oldPassword, 'hashedOldPassword');
      expect(bcrypt.hash).not.toHaveBeenCalled();
      expect(mockDatabaseService.user.update).not.toHaveBeenCalled();
    });
  });
});