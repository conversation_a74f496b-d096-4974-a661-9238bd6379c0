import { Test, TestingModule } from '@nestjs/testing';
import { AuthController } from './auth.controller';
import { AuthService } from './auth.service';
import { SignupRequestDto } from 'src/dto/signup.request.dto';
import { LoginRequestDto } from 'src/dto/login.request.dto';
import { ChangePasswordDto } from 'src/dto/change-password.dto';

describe('AuthController', () => {
  let authController: AuthController;
  let authService: AuthService;

  const mockAuthService = {
    signUp: jest.fn(),
    login: jest.fn(),
    changePassword: jest.fn(),
  };

  beforeEach(async () => {
    const moduleRef: TestingModule = await Test.createTestingModule({
      controllers: [AuthController],
      providers: [{ provide: AuthService, useValue: mockAuthService }],
    }).compile();

    authController = moduleRef.get<AuthController>(AuthController);
    authService = moduleRef.get<AuthService>(AuthService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('signUp', () => {
    it('should call authService.signUp with correct parameters and return result', async () => {
      const dto: SignupRequestDto = {
        name: 'John Doe',
        email: '<EMAIL>',
        password: 'password123',
      };
      const expectedResult = { message: 'User created' };
      mockAuthService.signUp.mockResolvedValue(expectedResult);

      const result = await authController.signUp(dto);

      expect(mockAuthService.signUp).toHaveBeenCalledWith(dto.name, dto.email, dto.password);
      expect(result).toEqual(expectedResult);
    });
  });

  describe('login', () => {
    it('should call authService.login with correct parameters and return result', async () => {
      const dto: LoginRequestDto = {
        email: '<EMAIL>',
        password: 'password123',
      };
      const expectedResult = { accessToken: 'jwt-token' };
      mockAuthService.login.mockResolvedValue(expectedResult);

      const result = await authController.login(dto);

      expect(mockAuthService.login).toHaveBeenCalledWith(dto.email, dto.password);
      expect(result).toEqual(expectedResult);
    });
  });

  describe('changePassword', () => {
    it('should call authService.changePassword with userId and dto', async () => {
      const user = { userId: 1 };
      const dto: ChangePasswordDto = {
        oldPassword: 'oldpass',
        newPassword: 'newpass',
      };
      const expectedResult = { message: 'Password changed' };
      mockAuthService.changePassword.mockResolvedValue(expectedResult);

      const req = { user };

      const result = await authController.changePassword(req, dto);

      expect(mockAuthService.changePassword).toHaveBeenCalledWith(user.userId, dto);
      expect(result).toEqual(expectedResult);
    });
  });
});
