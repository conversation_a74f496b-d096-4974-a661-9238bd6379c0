import { Injectable, ConflictException,UnauthorizedException } from '@nestjs/common';
import * as bcrypt from 'bcrypt';
import { JwtService } from '@nestjs/jwt';
import { DatabaseService } from 'src/database/database.service';
import { ChangePasswordDto } from 'src/dto/change-password.dto';
import { BadRequestException, NotFoundException } from '@nestjs/common';

@Injectable()
export class AuthService {
  constructor(
    private readonly databaseService: DatabaseService,
    private readonly jwtService: JwtService,
  ) {}

  async signUp(name: string, email: string, password: string) {
    const existingUser = await this.databaseService.user.findUnique({
      where: { email },
    });
    if (existingUser) {
      throw new ConflictException('Email already registered');
    }

    const hashedPassword = await bcrypt.hash(password, 10);

    const user = await this.databaseService.user.create({
      data: {
        name,
        email,
        password: hashedPassword,
      },
    });

    const response = {
      status: true,
      message: 'User signed up successfully',
      data: {
        id: user.id,
        name: user.name,
        email: user.email,
      },
    };

    return response;
  }

  async login(email: string, password: string) {
    const user = await this.databaseService.user.findUnique({
      where: { email },
    });
    if (!user) {
      throw new UnauthorizedException('Invalid credentials');
    }
    const passwordValid = await bcrypt.compare(password, user.password);
    if (!passwordValid) {
      throw new UnauthorizedException('Invalid credentials');
    }

    const payload = { userId: user.id, email: user.email };
    const token = this.jwtService.sign(payload);

    const response = {
      status: true,
      message: 'Login successful',
      data: {
        id: user.id,
        name: user.name,
        email: user.email,
        token,
      },
    };

    return response;
  }

  async changePassword(userId: string, dto: ChangePasswordDto) {
    const user = await this.databaseService.user.findUnique({ where: { id: userId } });
  
    if (!user) throw new NotFoundException('User not found');
  
    const passwordMatches = await bcrypt.compare(dto.oldPassword, user.password);
  
    if (!passwordMatches) {
      throw new BadRequestException('Old password is incorrect');
    }
  
    const newHashedPassword = await bcrypt.hash(dto.newPassword, 10);
  
    await this.databaseService.user.update({
      where: { id: userId },
      data: { password: newHashedPassword },
    });
  
    return {
      status: true,
      message: 'Password changed successfully',
      data: null, 
    };
  }
  
}
