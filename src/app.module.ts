import { Module } from '@nestjs/common';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { DatabaseModule } from './database/database.module';
import { AuthModule } from './auth/auth.module';
import { UserInfoModule } from './user-info/user-info.module';
import { TransactionsModule } from './transactions/transactions.module';
import { QRCodeModule } from './qrcode/qrcode.module';
import { ConfigModule } from '@nestjs/config';
import { EmailModule } from './email/email.module';
import { PasswordResetModule } from './password-reset/password-reset.module';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
    }),
  DatabaseModule, 
  AuthModule, 
  UserInfoModule, 
  TransactionsModule,
   QRCodeModule,
   EmailModule,
   PasswordResetModule
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}
