// Define our own interface based on the Prisma schema
export interface IUser {
  id: string;
  name: string;
  email: string;
  password: string;
  number?: string | null;
  billingAddress?: string | null;
  billingPostal?: string | null;
  billingCity?: string | null;
  billingCountry?: string | null;
  shippingAddress?: string | null;
  shippingPostal?: string | null;
  shippingCity?: string | null;
  shippingCountry?: string | null;
  createdAt: Date;
  updatedAt?: Date | null;
  resetToken?: string | null;
}

export class UserEntity implements Partial<IUser> {
  id: string;
  name: string;
  email: string;
  createdAt: Date;
  number?: string;
  billingAddress?: string;
  billingPostal?: string;
  billingCity?: string;
  billingCountry?: string;
  shippingAddress?: string;
  shippingPostal?: string;
  shippingCity?: string;
  shippingCountry?: string;
}
