import { Test, TestingModule } from '@nestjs/testing';
import { UserInfoController } from './user-info.controller';
import { UserInfoService } from './user-info.service';
import { UpdateUserInfoDto } from '../dto/update-user-info.dto';

describe('UserInfoController', () => {
  let controller: UserInfoController;
  let service: UserInfoService;

  const mockUserInfoService = {
    updateUser: jest.fn(),
    getUserById: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [UserInfoController],
      providers: [
        { provide: UserInfoService, useValue: mockUserInfoService },
      ],
    }).compile();

    controller = module.get<UserInfoController>(UserInfoController);
    service = module.get<UserInfoService>(UserInfoService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('updateUser', () => {
    it('should call updateUser on service and return updated user', async () => {
      const userId = 'user123';
      const dto: UpdateUserInfoDto = { name: 'New Name', email: '<EMAIL>' };
      const updatedUser = { id: userId, ...dto };

      mockUserInfoService.updateUser.mockResolvedValue(updatedUser);

      await expect(controller.updateUser(userId, dto)).resolves.toEqual(updatedUser);
      expect(mockUserInfoService.updateUser).toHaveBeenCalledWith(userId, dto);
    });
  });

  describe('getUser', () => {
    it('should return user by id', async () => {
      const userId = 'user123';
      const user = { id: userId, name: 'Test User', email: '<EMAIL>' };

      mockUserInfoService.getUserById.mockResolvedValue(user);

      await expect(controller.getUser(userId)).resolves.toEqual(user);
      expect(mockUserInfoService.getUserById).toHaveBeenCalledWith(userId);
    });
  });
});
