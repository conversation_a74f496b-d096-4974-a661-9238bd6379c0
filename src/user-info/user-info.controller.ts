import { Controller, <PERSON>, Body, Param, UseGuards, Get } from '@nestjs/common';
import { UserInfoService } from './user-info.service';
import { UpdateUserInfoDto } from '../dto/update-user-info.dto';
import { AuthGuard } from '@nestjs/passport'; 

@Controller('users')
export class UserInfoController {
  constructor(private readonly userInfoService: UserInfoService) {}

  @UseGuards(AuthGuard('jwt'))
  @Patch(':id') 
  updateUser(@Param('id') userId: string, @Body() updateUserDto: UpdateUserInfoDto) { 
    return this.userInfoService.updateUser(userId, updateUserDto);
  }

@UseGuards(AuthGuard('jwt'))
@Get(':id')
getUser(@Param('id') userId: string) {
  return this.userInfoService.getUserById(userId);
}
}