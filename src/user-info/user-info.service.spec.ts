import { Test, TestingModule } from '@nestjs/testing';
import { UserInfoService } from './user-info.service';
import { DatabaseService } from '../database/database.service';
import { NotFoundException } from '@nestjs/common';
import { UpdateUserInfoDto } from '../dto/update-user-info.dto';

describe('UserInfoService', () => {
  let service: UserInfoService;
  let databaseService: DatabaseService;

  const mockDatabaseService = {
    user: {
      findUnique: jest.fn(),
      update: jest.fn(),
    },
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UserInfoService,
        { provide: DatabaseService, useValue: mockDatabaseService },
      ],
    }).compile();

    service = module.get<UserInfoService>(UserInfoService);
    databaseService = module.get<DatabaseService>(DatabaseService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('updateUser', () => {
    it('should throw NotFoundException if user is not found', async () => {
      mockDatabaseService.user.findUnique.mockResolvedValue(null);

      const updateUserInfoDto: UpdateUserInfoDto = {
        name: 'John Doe',
        email: '<EMAIL>',
      };

      await expect(service.updateUser('user123', updateUserInfoDto)).rejects.toThrow(
        new NotFoundException('User not found'),
      );

      expect(mockDatabaseService.user.findUnique).toHaveBeenCalledWith({
        where: { id: 'user123' },
      });
      expect(mockDatabaseService.user.update).not.toHaveBeenCalled();
    });

    it('should update user successfully with partial DTO', async () => {
      const existingUser = {
        id: 'user123',
        name: 'Old Name',
        email: '<EMAIL>',
        number: null,
        billingAddress: '123 Old St',
        billingPostal: '12345',
        billingCity: 'Old City',
        billingCountry: 'Old Country',
        shippingAddress: '456 Old St',
        shippingPostal: '67890',
        shippingCity: 'Old Shipping City',
        shippingCountry: 'Old Shipping Country',
      };

      const updateUserInfoDto: UpdateUserInfoDto = {
        name: 'John Doe',
        email: '<EMAIL>',
      };

      const updatedUser = {
        ...existingUser,
        name: 'John Doe',
        email: '<EMAIL>',
      };

      mockDatabaseService.user.findUnique.mockResolvedValue(existingUser);
      mockDatabaseService.user.update.mockResolvedValue(updatedUser);

      const result = await service.updateUser('user123', updateUserInfoDto);

      expect(result).toEqual({
        status: true,
        message: 'Successfully saved',
        data: null,
      });

      expect(mockDatabaseService.user.findUnique).toHaveBeenCalledWith({
        where: { id: 'user123' },
      });

      expect(mockDatabaseService.user.update).toHaveBeenCalledWith({
        where: { id: 'user123' },
        data: {
          name: 'John Doe',
          email: '<EMAIL>',
          number: null,
          billingAddress: '123 Old St',
          billingPostal: '12345',
          billingCity: 'Old City',
          billingCountry: 'Old Country',
          shippingAddress: '456 Old St',
          shippingPostal: '67890',
          shippingCity: 'Old Shipping City',
          shippingCountry: 'Old Shipping Country',
        },
      });
    });

    it('should update user successfully with full DTO', async () => {
      const existingUser = {
        id: 'user123',
        name: 'Old Name',
        email: '<EMAIL>',
        number: null,
        billingAddress: '123 Old St',
        billingPostal: '12345',
        billingCity: 'Old City',
        billingCountry: 'Old Country',
        shippingAddress: '456 Old St',
        shippingPostal: '67890',
        shippingCity: 'Old Shipping City',
        shippingCountry: 'Old Shipping Country',
      };

      const updateUserInfoDto: UpdateUserInfoDto = {
        name: 'John Doe',
        email: '<EMAIL>',
        number: '1234567890',
        billingAddress: '123 New St',
        billingPostal: '54321',
        billingCity: 'New City',
        billingCountry: 'New Country',
        shippingAddress: '456 New St',
        shippingPostal: '09876',
        shippingCity: 'New Shipping City',
        shippingCountry: 'New Shipping Country',
      };

      const updatedUser = {
        ...existingUser,
        ...updateUserInfoDto,
      };

      mockDatabaseService.user.findUnique.mockResolvedValue(existingUser);
      mockDatabaseService.user.update.mockResolvedValue(updatedUser);

      const result = await service.updateUser('user123', updateUserInfoDto);

      expect(result).toEqual({
        status: true,
        message: 'Successfully saved',
        data: null,
      });

      expect(mockDatabaseService.user.update).toHaveBeenCalledWith({
        where: { id: 'user123' },
        data: updateUserInfoDto,
      });
    });
  });

  describe('getUserById', () => {
    it('should throw NotFoundException if user is not found', async () => {
      mockDatabaseService.user.findUnique.mockResolvedValue(null);

      await expect(service.getUserById('user123')).rejects.toThrow(
        new NotFoundException('User not found'),
      );

      expect(mockDatabaseService.user.findUnique).toHaveBeenCalledWith({
        where: { id: 'user123' },
        select: {
          id: true,
          name: true,
          email: true,
          number: true,
          billingAddress: true,
          billingPostal: true,
          billingCity: true,
          billingCountry: true,
          shippingAddress: true,
          shippingPostal: true,
          shippingCity: true,
          shippingCountry: true,
        },
      });
    });

    it('should return user data successfully', async () => {
      const mockUser = {
        id: 'user123',
        name: 'John Doe',
        email: '<EMAIL>',
        number: '1234567890',
        billingAddress: '123 Main St',
        billingPostal: '12345',
        billingCity: 'City',
        billingCountry: 'Country',
        shippingAddress: '456 Main St',
        shippingPostal: '67890',
        shippingCity: 'Shipping City',
        shippingCountry: 'Shipping Country',
      };

      mockDatabaseService.user.findUnique.mockResolvedValue(mockUser);

      const result = await service.getUserById('user123');

      expect(result).toEqual({
        status: true,
        message: 'User data retrieved successfully',
        data: mockUser,
      });

      expect(mockDatabaseService.user.findUnique).toHaveBeenCalledWith({
        where: { id: 'user123' },
        select: {
          id: true,
          name: true,
          email: true,
          number: true,
          billingAddress: true,
          billingPostal: true,
          billingCity: true,
          billingCountry: true,
          shippingAddress: true,
          shippingPostal: true,
          shippingCity: true,
          shippingCountry: true,
        },
      });
    });
  });
});