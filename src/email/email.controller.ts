import {<PERSON>, <PERSON>, Param, UseGuards, Req, BadRequestException, HttpCode} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { Request } from 'express';
import { DatabaseService } from '../database/database.service';
import { EmailService } from './email.service';
import { generateReceiptTemplate } from './templates/receipt.template';

@Controller('email')
export class EmailController {
  constructor(
    private prisma: DatabaseService,
    private emailService: EmailService,
  ) {}

  @UseGuards(AuthGuard('jwt'))
@Post('send-receipt/:transactionId')
@HttpCode(200)
async sendReceipt(
  @Param('transactionId') transactionId: string,
  @Req() req: Request,
) {
  const user = req.user as any;
  const userEmail = user.email;

  if (!transactionId) {
    throw new BadRequestException('Transaction ID is required');
  }

  const transaction = await this.prisma.transaction.findUnique({
    where: { id: transactionId },
  });

  if (!transaction) {
    throw new BadRequestException('Transaction not found');
  }

  const userData = await this.prisma.user.findUnique({
    where: { email: userEmail },
    select: { name: true },
  });

  if (!userData) {
    throw new BadRequestException('User not found');
  }

  const html = generateReceiptTemplate(transaction, userEmail, userData.name);

  await this.emailService.sendReceipt(userEmail, 'Your Transaction Receipt', html);

  return {
    status: true,
    message: 'Receipt sent successfully',
    data: {
      email: userEmail
    },
  };
}    
  }
