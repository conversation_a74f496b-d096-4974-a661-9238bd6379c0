import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as nodemailer from 'nodemailer';

@Injectable()
export class EmailService {
  private transporter: nodemailer.Transporter;

  constructor(private configService: ConfigService) {
    const host = this.configService.get<string>('MAIL_HOST');
    const port = this.configService.get<number>('MAIL_PORT');
    const user = this.configService.get<string>('MAIL_USER');
    const pass = this.configService.get<string>('MAIL_PASSWORD');

    if (!host || !port || !user || !pass) {
      throw new Error('SMTP configuration is incomplete');
    }

    this.transporter = nodemailer.createTransport({
      host,
      port,
      secure: false,
      auth: {
        user,
        pass,
      },
    });
  }

  async sendReceipt(to: string, subject: string, htmlContent: string) {
    const fromAddress = this.configService.get<string>('MAIL_FROM');

    try {
      const info = await this.transporter.sendMail({
        from: `"Hazel Pay" <${fromAddress}>`,
        to,
        subject,
        html: htmlContent,
      });
      return info;

    } catch (error) {
      throw error;
    }
  }
}
