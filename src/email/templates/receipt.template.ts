export const generateReceiptTemplate = (
  transaction: any,
  userEmail?: string,
  userName?: string,
) => {
  const formattedDate = new Date(transaction.createdAtUtc).toLocaleString();
  const formattedAmount = (transaction.amount / 100).toFixed(2);

  return `
  <div style="font-family: 'Segoe UI', Roboto, sans-serif; background-color: #f4f6f8; padding: 20px;">
    <table cellpadding="0" cellspacing="0" width="100%" style="max-width:600px; margin:auto; background:#fff; border-radius:12px; box-shadow:0 2px 8px rgba(0,0,0,0.05); padding:24px;">
      <!-- Logo -->
      <tr>
        <td align="center" style="padding-bottom: 16px;">
          <img src="https://www.hazelsone.com/assets/hazelsonelogo.png" alt="Hazel Pay" style="height:56px;" />
        </td>
      </tr>

      <tr><td><hr style="border: none; border-top: 1px solid #ccc;" /></td></tr>

      <!-- Greeting -->
      <tr>
        <td style="padding: 16px 0;">
          <h2 style="font-size: 20px; font-weight: 600; color: #333; margin: 0;">Hi, ${userName || 'User'}!</h2>
          <p style="font-size: 14px; color: #555; margin: 4px 0 0;">You have a new transaction on Hazel Pay.</p>
        </td>
      </tr>

      <tr><td><hr style="border: none; border-top: 1px solid #ccc;" /></td></tr>

      <!-- Transaction Details -->
      <tr>
        <td style="padding-top: 16px;">
          <h3 style="font-size: 18px; font-weight: 600; color: #333; margin-bottom: 10px;">Transaction Details</h3>
          ${generateRow('ID:', transaction.id)}
          ${generateRow('Time:', formattedDate)}
        </td>
      </tr>

      <!-- Amount Details -->
      <tr>
        <td style="padding-top: 16px;">
          <h3 style="font-size: 18px; font-weight: 600; color: #333; margin-bottom: 10px;">Amount Details</h3>
          ${generateRow('Amount:', formattedAmount)}
          ${generateRow('Currency:', transaction.currency)}
        </td>
      </tr>

      <!-- Additional Information -->
      <tr>
        <td style="padding-top: 16px;">
          <h3 style="font-size: 18px; font-weight: 600; color: #333; margin-bottom: 10px;">Additional Information</h3>
          ${userName ? generateRow('Sender:', userName) : ''}
          ${userEmail ? generateRow('Sender Email:', userEmail) : ''}
          ${generateRow('Recipient:', transaction.companyName)}
          ${generateRow('Status:', transaction.status?.toUpperCase())}
        </td>
      </tr>

      <tr><td><hr style="border: none; border-top: 1px solid #ccc;" /></td></tr>

      <!-- Footer -->
      <tr>
        <td style="text-align:center; font-size:13px; color:#777; padding-top: 12px;">
          Details of this transaction can be viewed on the Hazel Pay app.
        </td>
      </tr>
      <tr>
        <td style="text-align:center; font-size:13px; color:#444; padding-bottom: 12px;">
          Thank you for using Hazel Pay!
        </td>
      </tr>
    </table>
  </div>
  `;
};

// Helper function to generate rows
const generateRow = (label: string, value: string) => {
  return `
    <table cellpadding="0" cellspacing="0" width="100%" style="margin-bottom:8px;">
      <tr>
        <td style="font-size:14px; font-weight:600; color:#333; width:150px; vertical-align:top;">${label}</td>
        <td style="font-size:14px; color:#333; text-align:right; word-break:break-word;">${value}</td>
      </tr>
    </table>
  `;
};
