import { Test, TestingModule } from '@nestjs/testing';
import { EmailController } from './email.controller';
import { DatabaseService } from '../database/database.service';
import { EmailService } from './email.service';
import { BadRequestException } from '@nestjs/common';

jest.mock('./templates/receipt.template', () => ({
  generateReceiptTemplate: jest.fn(() => '<html>receipt</html>'),
}));

import { generateReceiptTemplate } from './templates/receipt.template';

describe('EmailController', () => {
  let emailController: EmailController;
  let databaseService: DatabaseService;
  let emailService: EmailService;

  const mockDatabaseService = {
    transaction: {
      findUnique: jest.fn(),
    },
  };

  const mockEmailService = {
    sendReceipt: jest.fn(),
  };

  beforeEach(async () => {
    const moduleRef: TestingModule = await Test.createTestingModule({
      controllers: [EmailController],
      providers: [
        { provide: DatabaseService, useValue: mockDatabaseService },
        { provide: EmailService, useValue: mockEmailService },
      ],
    }).compile();

    emailController = moduleRef.get<EmailController>(EmailController);
    databaseService = moduleRef.get<DatabaseService>(DatabaseService);
    emailService = moduleRef.get<EmailService>(EmailService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('sendReceipt', () => {
    const mockUser = { email: '<EMAIL>' };
    const mockReq = { user: mockUser } as any;

    it('should throw BadRequestException if transactionId is missing', async () => {
      await expect(emailController.sendReceipt('', mockReq)).rejects.toThrow(BadRequestException);
    });

    it('should throw BadRequestException if transaction is not found', async () => {
      mockDatabaseService.transaction.findUnique.mockResolvedValue(null);

      await expect(emailController.sendReceipt('nonexistent-id', mockReq)).rejects.toThrow(BadRequestException);

      expect(mockDatabaseService.transaction.findUnique).toHaveBeenCalledWith({
        where: { id: 'nonexistent-id' },
      });
    });

    it('should send receipt email and return success response', async () => {
      const transaction = { id: 'tx123', amount: 100 };
      mockDatabaseService.transaction.findUnique.mockResolvedValue(transaction);
      mockEmailService.sendReceipt.mockResolvedValue(undefined);

      const result = await emailController.sendReceipt('tx123', mockReq);

      expect(mockDatabaseService.transaction.findUnique).toHaveBeenCalledWith({
        where: { id: 'tx123' },
      });

      expect(generateReceiptTemplate).toHaveBeenCalledWith(transaction, mockUser.email);

      expect(mockEmailService.sendReceipt).toHaveBeenCalledWith(
        mockUser.email,
        'Your Transaction Receipt',
        '<html>receipt</html>',
      );

      expect(result).toEqual({
        status: true,
        message: 'Operation successful',
        data: {
          message: 'Receipt sent successfully',
          email: mockUser.email,
        },
      });
    });
  });
});
