import { EmailService } from './email.service';
import { ConfigService } from '@nestjs/config';
import * as nodemailer from 'nodemailer';

jest.mock('nodemailer');

describe('EmailService', () => {
  let emailService: EmailService;
  let mockConfigService: Partial<ConfigService>;
  let sendMailMock: jest.Mock;

  beforeEach(() => {
    mockConfigService = {
      get: jest.fn((key: string) => {
        const config = {
          MAIL_HOST: 'smtp.example.com',
          MAIL_PORT: 587,
          MAIL_USER: '<EMAIL>',
          MAIL_PASSWORD: 'password',
          MAIL_FROM: '<EMAIL>',
        };
        return config[key];
      }),
    };

    sendMailMock = jest.fn().mockResolvedValue({ messageId: '12345' });

    (nodemailer.createTransport as jest.Mock).mockReturnValue({
      sendMail: sendMailMock,
    });

    emailService = new EmailService(mockConfigService as ConfigService);
  });

  it('should send an email with correct parameters', async () => {
    const to = '<EMAIL>';
    const subject = 'Test Email';
    const html = '<p>Hello World</p>';

    const result = await emailService.sendReceipt(to, subject, html);

    expect(sendMailMock).toHaveBeenCalledWith({
      from: `"Hazel Pay" <<EMAIL>>`,
      to,
      subject,
      html,
    });

    expect(result).toEqual({ messageId: '12345' });
  });

  it('should throw an error if sendMail fails', async () => {
    sendMailMock.mockRejectedValueOnce(new Error('Send failed'));

    await expect(
      emailService.sendReceipt('<EMAIL>', 'Subject', '<p>Body</p>'),
    ).rejects.toThrow('Send failed');
  });

  it('should throw error if SMTP config is incomplete', () => {
    const badConfigService = {
      get: jest.fn(() => null),
    } as unknown as ConfigService;

    expect(() => new EmailService(badConfigService)).toThrow(
      'SMTP configuration is incomplete',
    );
  });
});
